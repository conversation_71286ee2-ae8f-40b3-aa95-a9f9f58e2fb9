---
description: Rules for Utility mixins and functions for mobile-first responsive design
globs: 
alwaysApply: false
---
# Mobile-First Utilities

Utility mixins and functions for mobile-first responsive design.

## Rules

### Breakpoint Mixins
- **Description:** Mixins for responsive breakpoints using variables from `_variables.scss`  
- **Pattern:** `theme/assets/scss/utils/*breakpoint*.scss`

### Media Query Utilities
- **Description:** Utilities for handling media queries in a mobile-first approach  
- **Pattern:** `theme/assets/scss/utils/*media*.scss`

### Responsive Helpers
- **Description:** Helper functions and mixins for responsive design  
- **Pattern:** `theme/assets/scss/utils/*responsive*.scss`
