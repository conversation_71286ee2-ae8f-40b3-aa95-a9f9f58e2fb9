---
description: SCSS files organization with variables and mobile-first approach
globs: theme/assets/scss/**/*.scss
alwaysApply: false
---
# SCSS Structure

SCSS files organization with variables and a mobile-first approach.

## Rules

### Variables
- **Description:** Global SCSS variables for breakpoints, colors, spacing, etc.  
- **Pattern:** `theme/assets/scss/_variables.scss`

### Main SCSS
- **Description:** Main SCSS file that imports all other files  
- **Pattern:** `theme/assets/scss/main.scss`

### Base Styles
- **Description:** Base styles for typography, reset, layout, etc.  
- **Pattern:** `theme/assets/scss/base/**/*.scss`

### Components
- **Description:** Reusable UI components  
- **Pattern:** `theme/assets/scss/components/**/*.scss`

### Content
- **Description:** Content-specific styles  
- **Pattern:** `theme/assets/scss/content/**/*.scss`

### Utilities
- **Description:** Utility classes and mixins  
- **Pattern:** `theme/assets/scss/utils/**/*.scss`
