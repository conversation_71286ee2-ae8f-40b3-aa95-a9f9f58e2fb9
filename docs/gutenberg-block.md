---
description: Rules on how to structure and build gutenberg blocks
globs: 
alwaysApply: false
---

# G<PERSON><PERSON> Blocks for wordpress in this theme

## Location
A block must always be located in a subfolder of the theme/blocks folder. The folder should have a kebab-case name of the block name.

### Structure
- name-of-block [Folder]
    - index.php
    - block.js

### Styling
Styles for the block must be located here:
theme/assets/scss/blocks/name-of-block.scss

The style file must be imported in the theme/assets/scss/main.scss

### index.php
Inside the folder we need a index.php to register all the block assets.
Here is an example of such a index.php file.

```php
<?php
/**
 * Block Name
 * @package StadtkapelleTheme
 */

function register_block_nameOfBlock()
{
    // Register the block editor script.
    wp_register_script(
        'name-of-block-editor-script',
        get_template_directory_uri() . '/blocks/name-of-block/build/block.js',
        array('wp-blocks', 'wp-element', 'wp-editor', 'wp-components'),
        filemtime(get_template_directory() . '/blocks/name-of-block/build/block.js')
    );

    // Register the block using register_block_type.
    register_block_type('stadtkapelle-theme/name-of-block', array(
        'editor_script'   => 'name-of-block-editor-script',
    ));
}
add_action('init', 'register_block_nameOfBlock');
```

### block.js
Inside the block folder we need a block.js which contains the logic and configuration of the block.
It should be built with es6 syntax and jsx, because it will be built into a es5 version later for wordpress to work with it.

Here is an example of such a file:

```js
import { registerBlockType } from "@wordpress/blocks";
import { RichText } from "@wordpress/block-editor";

registerBlockType("stadtkapelle-theme/name-of-block", {
  title: "Name of block",
  icon: "smiley",
  category: "common",
  attributes: {
    content: {
      type: "string",
      source: "html",
      selector: ".name-of-block-block",
      default: "Hello, World!",
    },
  },

  edit: ({ attributes, setAttributes, className }) => {
    const { content } = attributes;

    const onChangeContent = (newContent) => {
      setAttributes({ content: newContent });
    };

    return (
      <div className={className}>
        <RichText
          tagName="div"
          className="name-of-block-block"
          onChange={onChangeContent}
          value={content}
          placeholder="Enter text…"
        />
      </div>
    );
  },

  save: ({ attributes }) => {
    const { content } = attributes;

    return (
      <div className="name-of-block-block">
        <RichText.Content value={content} />
      </div>
    );
  },
});

```

## Registration
The block must be registered in the theme/functions.php. Inside the stadtkapelle_theme_register_blocks function the block must be registered with a line like this to load the index.php of the new block.

```php
// Register NameOfBlock Block
require_once get_template_directory() . '/blocks/name-of-block/index.php';
```





