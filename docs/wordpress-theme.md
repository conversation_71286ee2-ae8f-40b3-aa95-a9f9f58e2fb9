---
description: Basic rules for this wordPress theme with SCSS styling and Gutenberg blocks
globs: 
alwaysApply: false
---
# WordPress Theme Project

A WordPress theme with SCSS styling and Gutenberg blocks.

## Rules

### Theme Files
- **Description:** Main WordPress theme files  
- **Pattern:** `theme/*.php`

### Template Parts
- **Description:** Extracted template parts like header, footer, mobile menu  
- **Pattern:** `theme/template-parts/*.php`

### Gutenberg Blocks
- **Description:** Custom blocks for the Gutenberg editor  
- **Pattern:** `theme/blocks/**/*.{php,js}`

### SCSS Files
- **Description:** SCSS styling files  
- **Pattern:** `theme/assets/scss/**/*.scss`

### JavaScript Files
- **Description:** Theme JavaScript files  
- **Pattern:** `theme/assets/js/**/*.js`

### Functions
- **Description:** Theme functions  
- **Pattern:** `theme/functions/**/*.php`
