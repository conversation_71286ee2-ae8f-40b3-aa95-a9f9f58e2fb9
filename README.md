# Stadtkapelle WordPress Theme

## Getting Started

### 1. Start WordPress

The project uses Docker for local development. To start WordPress:

```bash
# Start the WordPress container
docker compose up

# WordPress will be available at:
# http://localhost:8080
```

### 2. Theme Development

The theme uses Sass for styling. The styles are organized in a modular structure:

```
theme/assets/scss/
├── base/           # Base styles, reset, typography
├── components/     # Reusable components
├── layouts/        # Layout-specific styles
└── utils/          # Variables, mixins, functions
```

#### Installing Dependencies

```bash
# Navigate to the theme directory
cd theme

# Install npm dependencies
npm install
```

#### Working with Styles

To compile Sass files:

```bash
# One-time compilation
npm run sass

# Watch for changes (recommended during development)
npm run sass:watch
```

#### Style Structure

- `main.scss` - Main entry point that imports all other Sass files
- `utils/_variables.scss` - Theme variables (colors, breakpoints, etc.)
- `base/_reset.scss` - CSS reset and base styles
- Add your component styles in `components/`
- Add your layout styles in `layouts/`

#### Breakpoints

The theme uses the following breakpoints (in rem):
- Small (sm): 36rem (576px)
- Medium (md): 48rem (768px)
- Large (lg): 62rem (992px)
- Extra Large (xl): 75rem (1200px)

Example usage:
```scss
@media (min-width: $breakpoint-md) {
    // Styles for medium screens and up
}
```