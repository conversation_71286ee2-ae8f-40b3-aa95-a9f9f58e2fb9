#!/bin/bash

# Get the directory where the script is located
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"

# Path to blocks directory (assuming it's in the same directory as the script)
BLOCKS_DIR="$SCRIPT_DIR/blocks"

# Change to the theme directory where the script is located
cd "$SCRIPT_DIR"

# Check if blocks directory exists
if [ ! -d "blocks" ]; then
    echo "Error: blocks directory not found"
    exit 1
fi

# Iterate through each directory in the blocks folder
for block_dir in blocks/*/ ; do
    if [ -d "$block_dir" ]; then
        # Remove trailing slash and 'blocks/' prefix from directory path
        block_name=$(basename "${block_dir%/}")
        
        echo "Building block in: blocks/$block_name"
        
        # Check if block.js exists in the directory
        if [ -f "blocks/$block_name/block.js" ]; then
            npx wp-scripts build "blocks/$block_name/block.js" --output-path="blocks/$block_name/build"
        else
            echo "Warning: block.js not found in blocks/$block_name"
        fi
    fi
done

echo "Build process completed!"
