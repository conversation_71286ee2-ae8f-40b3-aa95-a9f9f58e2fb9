import { registerBlockType } from "@wordpress/blocks";
import { RichText } from "@wordpress/block-editor";

registerBlockType("stadtkapelle-theme/stkm-block", {
  title: "STKM Block",
  icon: "smiley",
  category: "common",
  attributes: {
    content: {
      type: "string",
      source: "html",
      selector: ".my-custom-block",
      default: "Hello, World!",
    },
  },

  edit: ({ attributes, setAttributes, className }) => {
    const { content } = attributes;

    const onChangeContent = (newContent) => {
      setAttributes({ content: newContent });
    };

    return (
      <div className={className}>
        <RichText
          tagName="div"
          className="my-custom-block"
          onChange={onChangeContent}
          value={content}
          placeholder="Enter text…"
        />
      </div>
    );
  },

  save: ({ attributes }) => {
    const { content } = attributes;

    return (
      <div className="my-custom-block">
        <RichText.Content value={content} />
      </div>
    );
  },
});
