<?php
/**
 * Block Name
 * @package StadtkapelleTheme
 */

/**
 * Register custom Gutenberg block assets for both frontend and backend.
 */
function stadtkapelle_theme_register_block()
{
    // Register the block editor script.
    wp_register_script(
        'stadtkapelle-block',
        get_template_directory_uri() . '/blocks/stkm-block/build/block.js',
        array('wp-blocks', 'wp-element', 'wp-editor', 'wp-components'),
        filemtime(get_template_directory() . '/blocks/stkm-block/build/block.js')
    );

    // Register the block using register_block_type.
    register_block_type('stadtkapelle-theme/stkm-block', array(
        'editor_script'   => 'stadtkapelle-block',
    ));
}
add_action('init', 'stadtkapelle_theme_register_block');
