(()=>{"use strict";const t=window.wp.blocks,e=window.wp.blockEditor,c=window.ReactJSXRuntime;(0,t.registerBlockType)("stadtkapelle-theme/stkm-block",{title:"STKM Block",icon:"smiley",category:"common",attributes:{content:{type:"string",source:"html",selector:".my-custom-block",default:"Hello, World!"}},edit:({attributes:t,setAttributes:s,className:o})=>{const{content:l}=t;return(0,c.jsx)("div",{className:o,children:(0,c.jsx)(e.<PERSON>ex<PERSON>,{tagName:"div",className:"my-custom-block",onChange:t=>{s({content:t})},value:l,placeholder:"Enter text…"})})},save:({attributes:t})=>{const{content:s}=t;return(0,c.jsx)("div",{className:"my-custom-block",children:(0,c.jsx)(e.RichText.Content,{value:s})})}})})();