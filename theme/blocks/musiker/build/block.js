(()=>{"use strict";const e=window.wp.blocks,t=window.wp.blockEditor,s=window.wp.components,i=window.ReactJSXRuntime;(0,e.registerBlockType)("stadtkapelle-theme/musiker",{title:"<PERSON>sik<PERSON>",icon:"businessman",category:"common",attributes:{imageUrl:{type:"string",source:"attribute",selector:"img",attribute:"src"},imageId:{type:"number"},name:{type:"string",source:"html",selector:".musiker-name"},instrument:{type:"string",source:"html",selector:".musiker-instrument"},description:{type:"string",source:"html",selector:".musiker-description"}},edit:({attributes:e,setAttributes:r,className:n})=>{const{imageUrl:a,name:l,instrument:c,description:m}=e;return(0,i.jsxs)("div",{className:`${n} musiker-block`,style:{textAlign:"center",display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center"},children:[(0,i.jsx)("div",{className:"musiker-image-container",style:{height:"200px"},children:(0,i.jsx)(t.MediaUpload,{onSelect:e=>{r({imageUrl:e.url,imageId:e.id})},allowedTypes:["image"],value:e.imageId,render:({open:e})=>a?(0,i.jsx)("div",{style:{width:"200px",height:"200px",borderRadius:"50%",overflow:"hidden",display:"flex",alignItems:"center",justifyContent:"center",cursor:"pointer"},onClick:e,children:(0,i.jsx)("img",{src:a,alt:"Musiker Portrait",style:{width:"100%",height:"100%",objectFit:"cover"}})}):(0,i.jsx)(s.Button,{onClick:e,className:"button button-large",children:'"Bild auswählen"'})})}),(0,i.jsx)(t.RichText,{tagName:"h3",className:"musiker-name",value:l,onChange:e=>r({name:e}),placeholder:"Name des Musikers"}),(0,i.jsx)(t.RichText,{tagName:"div",className:"musiker-instrument",value:c,onChange:e=>r({instrument:e}),placeholder:"Instrument"}),(0,i.jsx)(t.RichText,{tagName:"div",className:"musiker-description",value:m,onChange:e=>r({description:e}),placeholder:"Beschreibung"})]})},save:({attributes:e})=>{const{imageUrl:t,name:s,instrument:r,description:n}=e;return(0,i.jsxs)("div",{className:"musiker-block",children:[(0,i.jsx)("div",{className:"musiker-image-container",children:t&&(0,i.jsx)("img",{src:t,alt:"Musiker Portrait"})}),s&&(0,i.jsx)("h3",{className:"musiker-name",children:s}),r&&(0,i.jsx)("div",{className:"musiker-instrument",children:r}),n&&(0,i.jsx)("div",{className:"musiker-description",children:n})]})}})})();