import { registerBlockType } from "@wordpress/blocks";
import { MediaUpload, RichText } from "@wordpress/block-editor";
import { Button } from "@wordpress/components";

registerBlockType("stadtkapelle-theme/musiker", {
    title: "Musik<PERSON>",
    icon: "businessman",
    category: "common",
    attributes: {
        imageUrl: {
            type: "string",
            source: "attribute",
            selector: "img",
            attribute: "src",
        },
        imageId: {
            type: "number",
        },
        name: {
            type: "string",
            source: "html",
            selector: ".musiker-name",
        },
        instrument: {
            type: "string",
            source: "html",
            selector: ".musiker-instrument",
        },
        description: {
            type: "string",
            source: "html",
            selector: ".musiker-description",
        },
    },

    edit: ({ attributes, setAttributes, className }) => {
        const { imageUrl, name, instrument, description } = attributes;

        const onSelectImage = (media) => {
            setAttributes({
                imageUrl: media.url,
                imageId: media.id,
            });
        };

        return (
            <div className={`${className} musiker-block`} style={{textAlign: 'center', display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center'}}>
                <div className="musiker-image-container" style={{height: '200px'}}>
                    <MediaUpload
                        onSelect={onSelectImage}
                        allowedTypes={["image"]}
                        value={attributes.imageId}
                        render={({ open }) => {
                            if(!imageUrl) {
                                return (
                                    <Button
                                        onClick={open}
                                        className="button button-large"
                                    >
                                        "Bild auswählen"
                                    </Button>
                                )
                            }
                            return (
                                <div style={{width: '200px', height: '200px', borderRadius: '50%', overflow: 'hidden', display: 'flex', alignItems: 'center', justifyContent: 'center', cursor: 'pointer'}} onClick={open}>
                                    <img src={imageUrl} alt="Musiker Portrait" style={{width: '100%', height: '100%', objectFit: 'cover'}} />
                                </div>
                            )
                        }}
                    />
                </div>

                <RichText
                    tagName="h3"
                    className="musiker-name"
                    value={name}
                    onChange={(name) => setAttributes({ name })}
                    placeholder="Name des Musikers"
                />

                <RichText
                    tagName="div"
                    className="musiker-instrument"
                    value={instrument}
                    onChange={(instrument) => setAttributes({ instrument })}
                    placeholder="Instrument"
                />

                <RichText
                    tagName="div"
                    className="musiker-description"
                    value={description}
                    onChange={(description) => setAttributes({ description })}
                    placeholder="Beschreibung"
                />
            </div>
        );
    },

    save: ({ attributes }) => {
        const { imageUrl, name, instrument, description } = attributes;

        return (
            <div className="musiker-block">
                <div className="musiker-image-container">
                    {imageUrl && <img src={imageUrl} alt="Musiker Portrait" />}
                </div>
                
                {name && <h3 className="musiker-name">{name}</h3>}
                
                {instrument && (
                    <div className="musiker-instrument">{instrument}</div>
                )}
                
                {description && (
                    <div className="musiker-description">{description}</div>
                )}
            </div>
        );
    },
}); 