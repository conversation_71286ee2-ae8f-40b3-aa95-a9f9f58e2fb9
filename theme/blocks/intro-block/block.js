import { registerBlockType } from "@wordpress/blocks";
import { MediaUpload, RichText } from "@wordpress/block-editor";
import { Button } from "@wordpress/components";

registerBlockType("stadtkapelle-theme/intro-block", {
    title: "Intro Block",
    icon: "cover-image",
    category: "common",
    attributes: {
        desktopImage: {
            type: "string"
        },
        mobileImage: {
            type: "string"
        },
        desktopImageId: {
            type: "number"
        },
        mobileImageId: {
            type: "number"
        },
        mainText: {
            type: "string",
            source: "html",
            selector: ".intro-text"
        }
    },

    edit: ({ attributes, setAttributes, className }) => {
        const { desktopImage, mobileImage, mainText } = attributes;

        const onSelectDesktopImage = (media) => {
            setAttributes({
                desktopImage: media.url,
                desktopImageId: media.id,
            });
        };

        const onSelectMobileImage = (media) => {
            setAttributes({
                mobileImage: media.url,
                mobileImageId: media.id,
            });
        };

        return (
            <div className={`${className} intro-block`}>
                <div className="intro-content">
                    <RichText
                        tagName="h1"
                        className="intro-text"
                        value={mainText}
                        onChange={(mainText) => setAttributes({ mainText })}
                        placeholder="Blasmusik aus dem Herzen"
                    />
                </div>

                <div className="image-controls">
                    <div className="desktop-image-control">
                        <MediaUpload
                            onSelect={onSelectDesktopImage}
                            allowedTypes={["image"]}
                            value={attributes.desktopImageId}
                            render={({ open }) => (
                                <Button
                                    onClick={open}
                                    className="button button-large"
                                    style={{
                                        backgroundColor: 'white',
                                        padding: '0.5rem 1rem',
                                        marginBottom: '0.5rem'
                                    }}
                                >
                                    {desktopImage ? 'Change Desktop Image' : 'Select Desktop Image'}
                                </Button>
                            )}
                        />
                    </div>
                    <div className="mobile-image-control">
                        <MediaUpload
                            onSelect={onSelectMobileImage}
                            allowedTypes={["image"]}
                            value={attributes.mobileImageId}
                            render={({ open }) => (
                                <Button
                                    onClick={open}
                                    className="button button-large"
                                    style={{
                                        backgroundColor: 'white',
                                        padding: '0.5rem 1rem'
                                    }}
                                >
                                    {mobileImage ? 'Change Mobile Image' : 'Select Mobile Image'}
                                </Button>
                            )}
                        />
                    </div>
                </div>
            </div>
        );
    },

    save: ({ attributes }) => {
        const { desktopImage, mobileImage, mainText } = attributes;

        return (
            <div className="intro-block">
                <div 
                    className="intro-block__desktop-image"
                    style={{
                        backgroundImage: `url(${desktopImage})`,
                    }}
                />
                <div 
                    className="intro-block__mobile-image"
                    style={{
                        backgroundImage: `url(${mobileImage})`,
                    }}
                />

                <div className="intro-logo">
                    <img 
                        src="/wp-content/themes/stadtkapelle-theme/assets/images/logo_hoch_white.svg" 
                        alt="Stadtkapelle Melk Logo"
                    />
                </div>

                <div className="intro-content">
                    {mainText && <h1 className="intro-text">{mainText}</h1>}
                </div>
            </div>
        );
    },
});
