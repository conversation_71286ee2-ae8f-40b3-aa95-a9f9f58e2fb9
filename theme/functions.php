<?php
// Exit if accessed directly.
if (! defined('ABSPATH')) {
    exit;
}

/**
 * Enqueue theme styles and scripts.
 */
function stadtkapelle_theme_enqueue_assets()
{
    // Enqueue the main stylesheet.
    wp_enqueue_style('stadtkapelle-theme-style', get_stylesheet_uri());
    wp_enqueue_style('stadtkapelle-theme-style-extended', get_template_directory_uri() . '/assets/css/output.css', array(), filemtime(get_template_directory() . '/assets/css/output.css'));
    // add_editor_style('assets/css/output.css');

    // Enqueue editor style for Gutenberg.
    add_theme_support('editor-styles');
    add_editor_style('assets/css/editor-style.css');

    // Enqueue custom JavaScript for the hamburger menu.
    wp_enqueue_script('stadtkapelle-menu-script', get_template_directory_uri() . '/assets/js/menu.js', array(), filemtime(get_template_directory() . '/assets/js/menu.js'), true);
}
add_action('wp_enqueue_scripts', 'stadtkapelle_theme_enqueue_assets');

/**
 * Register theme menus.
 */
function stadtkapelle_theme_register_menus()
{
    register_nav_menus(
        array(
            'main-menu'       => __('Main Menu', 'stadtkapelle-theme'),
            'highlights'      => __('Highlights', 'stadtkapelle-theme'),
            'explore'         => __('Explore', 'stadtkapelle-theme'),
            'main-menu-meta'  => __('Main Menu Meta', 'stadtkapelle-theme'), // New Menu
        )
    );
}
add_action('after_setup_theme', 'stadtkapelle_theme_register_menus');

// Enable support for featured images
function stadtkapelle_theme_setup()
{
    add_theme_support('post-thumbnails'); // Enables featured images for posts and pages
}
add_action('after_setup_theme', 'stadtkapelle_theme_setup');

/**
 * Register custom blocks
 */
function stadtkapelle_theme_register_blocks() {
    // Register STKM Block
    require_once get_template_directory() . '/blocks/stkm-block/index.php';

    // Register Musiker Block
    require_once get_template_directory() . '/blocks/musiker/index.php';

    // Register Intro Block
    require_once get_template_directory() . '/blocks/intro-block/index.php';
}
add_action('init', 'stadtkapelle_theme_register_blocks', 5);
