<?php

/**
 * Register the "Three Latest Posts" Gutenberg block
 */
function mytheme_register_three_latest_posts_block()
{

    // 1) Register the block editor script
    wp_register_script(
        'mytheme-three-latest-posts-editor',
        get_template_directory_uri() . '/blocks/three-latest-posts/index.js',
        array('wp-blocks', 'wp-element', 'wp-editor', 'wp-components', 'wp-i18n'),
        filemtime(get_template_directory() . '/blocks/three-latest-posts/index.js'),
        true
    );

    // 2) Register the block with a render callback
    register_block_type('mytheme/three-latest-posts', array(
        'editor_script'   => 'mytheme-three-latest-posts-editor',
        'render_callback' => 'mytheme_render_three_latest_posts_block',
    ));
}
add_action('init', 'mytheme_register_three_latest_posts_block');

/**
 * Render callback for the "Three Latest Posts" block.
 * Dynamically generates the HTML for the block on the front-end.
 */
function mytheme_render_three_latest_posts_block($attributes)
{
    // Query the three latest posts
    $args = array(
        'post_type'      => 'post',
        'posts_per_page' => 3,
    );
    $the_query = new WP_Query($args);

    // If no posts, return empty string
    if (! $the_query->have_posts()) {
        return '';
    }

    // Start building output HTML
    $html = '<div class="three-latest-posts-block flex flex-col md:flex-row gap-2"';

    while ($the_query->have_posts()) {
        $the_query->the_post();

        $html .= '<div class="three-latest-posts-item flex flex-col flex-1"';

        // 1) Post Image
        if (has_post_thumbnail()) {
            $html .= '<div class="post-image">' . get_the_post_thumbnail(get_the_ID(), 'medium') . '</div>';
        }

        // 2) Post Title
        $html .= '<h2 class="post-title">' . esc_html(get_the_title()) . '</h2>';

        // 3) Post Date
        $html .= '<div class="post-date">' . esc_html(get_the_date()) . '</div>';

        // 4) Excerpt
        $html .= '<div class="post-excerpt">' . esc_html(get_the_excerpt()) . '</div>';

        // 5) “mehr” link
        $html .= sprintf(
            '<div class="post-read-more"><a href="%s">%s</a></div>',
            esc_url(get_the_permalink()),
            esc_html__('mehr', 'mytheme')
        );

        $html .= '</div>'; // .three-latest-posts-item
    }

    wp_reset_postdata();

    $html .= '</div>'; // .three-latest-posts-block

    // “Alle Neuigkeiten” button
    // If you have your “Posts Page” set in WP Admin > Settings > Reading
    // get_option('page_for_posts') returns that page ID.
    $blog_page_id = get_option('page_for_posts');
    if ($blog_page_id) {
        $html .= sprintf(
            '<div class="all-news-button" style="margin-top: 20px;"><a href="%s">%s</a></div>',
            esc_url(get_permalink($blog_page_id)),
            esc_html__('Alle Neuigkeiten', 'mytheme')
        );
    }

    return $html;
}
