// Merriweather Font Family
@font-face {
    font-family: 'Merriweather';
    src: url('/wp-content/themes/stadtkapelle-theme/assets/fonts/Merriweather/Merriweather-Regular.ttf') format('truetype');
    font-weight: 400;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Merriweather';
    src: url('/wp-content/themes/stadtkapelle-theme/assets/fonts/Merriweather/Merriweather-Italic.ttf') format('truetype');
    font-weight: 400;
    font-style: italic;
    font-display: swap;
}

@font-face {
    font-family: 'Merriweather';
    src: url('/wp-content/themes/stadtkapelle-theme/assets/fonts/Merriweather/Merriweather-Light.ttf') format('truetype');
    font-weight: 300;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Merriweather';
    src: url('/wp-content/themes/stadtkapelle-theme/assets/fonts/Merriweather/Merriweather-LightItalic.ttf') format('truetype');
    font-weight: 300;
    font-style: italic;
    font-display: swap;
}

@font-face {
    font-family: 'Merriweather';
    src: url('/wp-content/themes/stadtkapelle-theme/assets/fonts/Merriweather/Merriweather-Bold.ttf') format('truetype');
    font-weight: 700;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Merriweather';
    src: url('/wp-content/themes/stadtkapelle-theme/assets/fonts/Merriweather/Merriweather-BoldItalic.ttf') format('truetype');
    font-weight: 700;
    font-style: italic;
    font-display: swap;
}

@font-face {
    font-family: 'Merriweather';
    src: url('/wp-content/themes/stadtkapelle-theme/assets/fonts/Merriweather/Merriweather-Black.ttf') format('truetype');
    font-weight: 900;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Merriweather';
    src: url('/wp-content/themes/stadtkapelle-theme/assets/fonts/Merriweather/Merriweather-BlackItalic.ttf') format('truetype');
    font-weight: 900;
    font-style: italic;
    font-display: swap;
}

// Inter Variable Font Family
@font-face {
    font-family: 'Inter';
    src: url('/wp-content/themes/stadtkapelle-theme/assets/fonts/Inter/Inter-VariableFont_opsz\,wght.ttf') format('truetype-variations');
    font-weight: 100 900;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Inter';
    src: url('/wp-content/themes/stadtkapelle-theme/assets/fonts/Inter/Inter-Italic-VariableFont_opsz\,wght.ttf') format('truetype-variations');
    font-weight: 100 900;
    font-style: italic;
    font-display: swap;
}

// Font utility classes
.serif {
    font-family: 'Merriweather', Georgia, 'Times New Roman', serif;
}

$font-serif: 'Merriweather', Georgia, 'Times New Roman', serif;
$font-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen-Sans, Ubuntu, Cantarell, 'Helvetica Neue', sans-serif;

.sans {
    font-family: $font-sans;
}

html {
    font-family: $font-sans;
}