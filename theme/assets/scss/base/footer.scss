@use "../variables";

footer {
  background-color: variables.$primary-color;
  padding: 3rem 1rem;
  margin-top: 5rem;
  color: #000;

  // Container for all content
  .container {
    display: block;
    max-width: variables.$container-max-width;
    margin: 0 auto;

    @media (min-width: variables.$breakpoint-md) {
      display: grid;
      gap: 6rem;
      grid-template-columns: 1fr 1fr 1fr 1fr;
      justify-content: space-between;
      align-items: start;
    }
  }

  // Logo section
  .footer-logo {
    text-align: center;

    @media (min-width: variables.$breakpoint-md) {
      flex: 1;
      text-align: left;
    }

    img {
      width: 100%;
      max-width: 10rem;
      height: auto;
      margin-bottom: 1rem;
    }

    .footer-brand {
      font-weight: bold;
      text-transform: uppercase;
      line-height: 1.2;
    }
  }

  // Navigation sections
  nav,
  .contact-section {
    @media (min-width: variables.$breakpoint-md) {
      flex: 1;
    }

    h2 {
      font-size: 1.75rem;
      font-weight: bold;
      margin-bottom: 1rem;
    }

    ul {
      list-style: none;
      padding: 0;
      margin: 0;

      li {
        margin-bottom: 0.5rem;

        a {
          text-decoration: none;
          color: inherit;

          &:hover {
            text-decoration: underline;
          }
        }
      }
    }
  }

  // Contact section specific styles
  .contact-section {
    address {
      font-style: normal;
      margin-bottom: 1rem;
    }

    .social-links {
      display: flex;
      gap: 1rem;
      margin-top: 1rem;

      a {
        color: #000;
        font-size: 1.25rem;

        &:hover {
          opacity: 0.8;
        }
      }
    }
  }

  // Copyright section
  .copyright {
    text-align: center;
    margin-top: 3rem;
    padding-top: 1rem;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    font-size: 0.875rem;

    p {
      margin: 0;
    }
  }
}
