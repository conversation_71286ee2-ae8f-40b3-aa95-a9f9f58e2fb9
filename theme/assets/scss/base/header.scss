@use "../_variables";

.header {
    &__inner {
        display: flex;
        justify-content: space-between;
        align-items: center;

        @media (min-width: variables.$breakpoint-lg) {
            padding-top: 2rem;
            padding-bottom: 1.5rem;
        }
    }

    .logo {
        &__text {
            display: none;
        }

        &__mobile {
            @media (min-width: variables.$breakpoint-lg) {
                display: none;
            }
        }

        &__desktop {
            display: none;
            width: 29rem;
            height: auto;
            @media (min-width: variables.$breakpoint-lg) {
                display: block;
            }
        }
    }
}

.hamburger {
    background-color: transparent;
    border: none;
    padding: 0;

    @media (min-width: variables.$breakpoint-lg) {
        transform: translateY(0.5rem);
    }

    img {
        width: 2rem;
        height: 2rem;
    }
}
