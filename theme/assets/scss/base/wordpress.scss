// Import variables
@use "../_variables";

// Container
.container {
  width: 100%;
  margin-right: auto;
  margin-left: auto;
  padding-right: variables.$spacing-unit;
  padding-left: variables.$spacing-unit;
  max-width: variables.$container-max-width;
}

.alignfull {
  margin: 2rem calc(50% - 50vw);
  max-width: 100vw;
  width: 100vw;
}

.alignwide {
  margin: 2rem calc(25% - 25vw);
  max-width: 75vw;
  width: 75vw;
}

.aligncenter {
  margin-left: auto;
  margin-right: auto;
  text-align: center;
}

.alignleft {
  @media (min-width: variables.$breakpoint-sm) {
    float: left;
    margin-right: variables.$spacing-unit;
  }
}

.alignright {
  @media (min-width: variables.$breakpoint-sm) {
    float: right;
    margin-left: variables.$spacing-unit;
  }
}

// Grid System
.wp-block-columns {
  display: flex;
  flex-wrap: wrap;
  gap: variables.$spacing-unit;
  margin-bottom: variables.$spacing-unit;

  // Mobile first - stack columns
  .wp-block-column {
    flex: 0 0 100%;
    width: 100%;
  }

  // Tablet and up - enable columns
  @media (min-width: variables.$breakpoint-md) {
    flex-wrap: nowrap;

    .wp-block-column {
      flex: 1;
      min-width: 0;
    }

    // Handle specific column width classes
    .wp-block-column[style*="flex-basis"] {
      flex-grow: 0;
    }
  }
}

// Common Block Layouts
.wp-block-group {
  width: 100%;

  &.has-background {
    padding: variables.$spacing-unit;
  }
}

.wp-block-media-text {
  display: grid;
  grid-template-rows: auto;
  align-items: center;
  grid-template-columns: 1fr;
  gap: variables.$spacing-unit;

  @media (min-width: variables.$breakpoint-md) {
    grid-template-columns: 50% 1fr;
  }

  &.has-media-on-the-right {
    @media (min-width: variables.$breakpoint-md) {
      grid-template-columns: 1fr 50%;
    }
  }
}

// Gallery Layout
.wp-block-gallery {
  display: grid;
  gap: variables.$spacing-unit;
  margin: 0;
  padding: 0;
  list-style: none;

  // Default mobile layout - 1 column
  grid-template-columns: 1fr;

  // Tablet - 2 columns
  @media (min-width: variables.$breakpoint-sm) {
    grid-template-columns: repeat(2, 1fr);
  }

  // Desktop - 3 columns
  @media (min-width: variables.$breakpoint-lg) {
    grid-template-columns: repeat(3, 1fr);
  }

  .blocks-gallery-item {
    width: 100%;
    margin: 0;
    display: flex;
    align-items: center;
    justify-content: center;

    figure {
      width: 100%;
      height: 100%;
      margin: 0;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
  }
}

// Cover Block Layout
.wp-block-cover {
  display: flex;
  position: relative;
  align-items: center;
  justify-content: center;
  min-height: 25rem;
  width: 100%;
  padding: variables.$spacing-unit;

  &.alignfull {
    padding-left: 0;
    padding-right: 0;
  }

  .wp-block-cover__inner-container {
    width: 100%;
    max-width: variables.$container-max-width;
    margin: 0 auto;
    position: relative;
    z-index: 1;
  }
}

// Button Block Layout
.wp-block-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: variables.$spacing-unit;
  margin-bottom: variables.$spacing-unit;

  &.is-content-justification-center {
    justify-content: center;
  }

  &.is-content-justification-right {
    justify-content: flex-end;
  }

  &.is-content-justification-left {
    justify-content: flex-start;
  }
}

// List Block Layout
.wp-block-list {
  padding-left: 2rem;
  margin: variables.$spacing-unit 0;
}

// Table Layout
.wp-block-table {
  width: 100%;
  margin: variables.$spacing-unit 0;
  overflow-x: auto;
  display: block;

  @media (min-width: variables.$breakpoint-md) {
    display: table;
  }

  table {
    width: 100%;
    border-collapse: collapse;
  }

  td,
  th {
    padding: 0.5rem;
  }
}

// Quote Block Layout
.wp-block-quote {
  margin: variables.$spacing-unit 0;
  padding-left: variables.$spacing-unit;

  @media (min-width: variables.$breakpoint-md) {
    padding-left: 2rem;
  }
}

// Separator Block Layout
.wp-block-separator {
  margin: variables.$spacing-unit 0;
  width: 100%;

  &.is-style-wide {
    width: 100%;
  }

  &.is-style-dots {
    width: 100%;
    text-align: center;
  }
}
