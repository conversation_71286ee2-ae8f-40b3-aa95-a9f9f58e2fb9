.musiker-block {
    text-align: center;
    max-width: 37.5rem;
    margin: 0 auto;
    padding: 2rem 1rem;
    display: flex;
    flex-direction: column;
    align-items: center;

    .musiker-image-container {
        width: 15rem;
        height: 15rem;
        margin: 0 auto 1.5rem;
        border-radius: 50%;
        overflow: hidden;
        display: flex;
        align-items: center;
        justify-content: center;
        
        img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
    }

    .musiker-name {
        font-weight: bold;
        font-size: 1.5rem;
        margin: 0 0 0.5rem;
    }

    .musiker-instrument {
        color: #666;
        margin-bottom: 1rem;
    }

    .musiker-description {
        line-height: 1.6;
    }

    // Editor styles
    .wp-block-stadtkapelle-theme-musiker {
        .image-button {
            padding: 0;
            border: none;
            border-radius: 50%;
            overflow: hidden;
            width: 15rem;
            height: 15rem;

            img {
                width: 100%;
                height: 100%;
                object-fit: cover;
            }
        }

        .button {
            display: block;
            margin: 0 auto;
        }
    }
} 