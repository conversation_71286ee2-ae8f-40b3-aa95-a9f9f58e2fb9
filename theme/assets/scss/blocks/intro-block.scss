@use "../base/fonts.scss";
@use "../variables.scss";

.intro-block {
    position: relative;
    height: 90vh;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    align-items: flex-end;
    margin-bottom: 5rem;

    &__desktop-image,
    &__mobile-image {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-size: cover;
        background-position: center;
    }

    &__desktop-image {
        display: none;
        @media (min-width: variables.$breakpoint-md) {
            display: block;
        }
    }

    &__mobile-image {
        display: block;
        @media (min-width: variables.$breakpoint-md) {
            display: none;
        }
    }

    .intro-logo {
        position: absolute;
        top: 2rem;
        left: 2rem;
        z-index: 1;

        @media (min-width: variables.$breakpoint-md) {
            top: 0;
            bottom: 0;
            height: 100%;
            display: flex;
            align-items: center;
        }

        img {
            width: 150px;
            height: auto;

            @media (min-width: variables.$breakpoint-md) {
                width: 15rem;
            }
        }
    }

    .intro-content {
        position: relative;
        z-index: 1;
        color: white;
        text-align: right;
        margin-left: auto;
        padding: 2rem;

        @media (min-width: variables.$breakpoint-md) {
            max-width: 60%;
        }

        .intro-text {
            font-size: 1rem;
            font-family: fonts.$font-serif;
            margin: 0;
            font-weight: bold;

            @media (min-width: variables.$breakpoint-md) {
                font-size: 2.5rem;
            }
        }
    }

    // Editor-specific styles
    .image-controls {
        position: absolute;
        top: 1rem;
        right: 1rem;
        z-index: 1;
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
    }
}
