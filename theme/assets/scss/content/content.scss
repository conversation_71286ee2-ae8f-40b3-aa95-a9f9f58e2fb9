@use "../base/fonts";
@use "../variables";

/* This file is the entrypoint for all the content related styling for the gutenberg editor. */
article.page {
  > h2 {
    display: none;
  }
  .post-content {
    // the main content of the gutenberg editor

    // every root block layout
    > *:not(.wp-block-cover) {
      margin-right: auto;
      margin-left: auto;
      width: 100%;
      max-width: variables.$container-max-width;
      padding-left: 1rem;
      padding-right: 1rem;
    }

    // headling
    .wp-block-heading {
      font-family: fonts.$font-serif;
      margin-top: 2rem;
      margin-bottom: 1rem;
    }

    h1.wp-block-heading {
      font-size: 2rem;

      & + .small {
        margin-top: 0;
        margin-bottom: 3rem;
        text-transform: uppercase;
        font-size: 1.25rem;
      }
    }

    h2.wp-block-heading {
      font-size: 1.5rem;
    }

    h3.wp-block-heading {
      font-family: fonts.$font-sans;
      font-size: 1.25rem;
    }

    // cover
    .wp-block-cover + .wp-block-heading {
      margin-top: 5rem;
    }

    // image
    figure.wp-block-image {
      margin-top: 2rem;
      margin-bottom: 2rem;
      padding: 0;
      border-left: 0.5rem solid variables.$primary-color;
    }

    p + details {
      margin-top: 2rem;
    }

    // aufklapp bloecke
    details {
      margin: 1rem 0;

      summary {
        font-size: 1.5rem;
        font-family: fonts.$font-serif;
        font-weight: 300;
      }
    }
  }
}
