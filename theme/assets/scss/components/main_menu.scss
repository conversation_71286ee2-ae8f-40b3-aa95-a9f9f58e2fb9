/* Main Menu styling */

/* Slide Menu Styles */
.slide-menu {
  position: fixed;
  top: 0;
  right: -100%;
  width: 100%;
  height: 100%;
  background-color: #f5f1e9; /* Beige/cream background */
  transition: right 0.3s ease;
  z-index: 1000;
  overflow-y: auto;
  padding: 2rem;

  &.open {
    right: 0;
  }

  #main-menu {
    background-color: #333;
    padding: 10px;

    ul {
      list-style: none;
      margin: 0;
      padding: 0;
      display: flex;
    }

    li {
      margin-right: 20px;
    }

    a {
      color: #fff;
      text-decoration: none;
    }
  }

  /* Highlights Menu styling */
  #highlights {
    background-color: #f0f0f0;
    padding: 10px;
  }

  /* Explore Menu styling */
  #explore {
    background-color: #ddd;
    padding: 10px;
  }
}

.close-menu {
  position: absolute;
  top: 20px;
  right: 30px;
  font-size: 24px;
  background: none;
  border: none;
  cursor: pointer;
  color: #000;
}

.menu-container {
  margin-top: 60px;
  padding: 20px;
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
}

.menu-section {
  margin-bottom: 40px;
  text-align: left;

  ul {
    list-style: none;
    padding: 0;
  }

  li {
    margin-bottom: 1rem;
  }

  a {
    text-decoration: none;
    color: #000;
    font-size: 1.25rem;
    font-weight: 500;

    &:hover {
      opacity: 0.7;
    }
  }
}

/* Social Icons Section */
.social-icons {
  display: flex;
  justify-content: flex-start;
  gap: 1.5rem;
  margin: 2rem 0;
  padding: 1rem 0;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);

  a {
    color: #000;
    font-size: 1.5rem;

    &:hover {
      opacity: 0.7;
    }
  }
}

/* Overlay to prevent background scrolling when menu is open */
body.menu-open {
  overflow: hidden;
}

/* Responsive Adjustments */
@media (min-width: 768px) {
  /* Ensure hamburger menu is visible on desktop */
  .hamburger {
    display: block;
  }

  .slide-menu {
    max-width: 400px;
  }
}
