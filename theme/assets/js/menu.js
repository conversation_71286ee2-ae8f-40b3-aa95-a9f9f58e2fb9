document.addEventListener('DOMContentLoaded', function () {
    const hamburger = document.querySelector('.hamburger');
    const slideMenu = document.getElementById('slide-menu');
    const closeMenuButton = document.querySelector('.close-menu');

    // Function to open the menu
    function openMenu() {
        hamburger.classList.add('active');
        slideMenu.classList.add('open');
        document.body.classList.add('menu-open');
        slideMenu.setAttribute('aria-hidden', 'false');
        hamburger.setAttribute('aria-expanded', 'true');
    }

    // Function to close the menu
    function closeMenu() {
        hamburger.classList.remove('active');
        slideMenu.classList.remove('open');
        document.body.classList.remove('menu-open');
        slideMenu.setAttribute('aria-hidden', 'true');
        hamburger.setAttribute('aria-expanded', 'false');
    }

    // Event listeners
    hamburger.addEventListener('click', function () {
        const isOpen = slideMenu.classList.contains('open');
        if (isOpen) {
            closeMenu();
        } else {
            openMenu();
        }
    });

    closeMenuButton.addEventListener('click', closeMenu);

    // Close the menu when clicking outside of it
    slideMenu.addEventListener('click', function (e) {
        if (e.target === slideMenu) {
            closeMenu();
        }
    });

    // Optional: Close the menu when pressing the Escape key
    document.addEventListener('keydown', function (e) {
        if (e.key === 'Escape' && slideMenu.classList.contains('open')) {
            closeMenu();
        }
    });
});
