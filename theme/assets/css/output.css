@font-face {
  font-family: "Merriweather";
  src: url("/wp-content/themes/stadtkapelle-theme/assets/fonts/Merriweather/Merriweather-Regular.ttf") format("truetype");
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: "Merriweather";
  src: url("/wp-content/themes/stadtkapelle-theme/assets/fonts/Merriweather/Merriweather-Italic.ttf") format("truetype");
  font-weight: 400;
  font-style: italic;
  font-display: swap;
}
@font-face {
  font-family: "Merriweather";
  src: url("/wp-content/themes/stadtkapelle-theme/assets/fonts/Merriweather/Merriweather-Light.ttf") format("truetype");
  font-weight: 300;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: "Merriweather";
  src: url("/wp-content/themes/stadtkapelle-theme/assets/fonts/Merriweather/Merriweather-LightItalic.ttf") format("truetype");
  font-weight: 300;
  font-style: italic;
  font-display: swap;
}
@font-face {
  font-family: "Merriweather";
  src: url("/wp-content/themes/stadtkapelle-theme/assets/fonts/Merriweather/Merriweather-Bold.ttf") format("truetype");
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: "Merriweather";
  src: url("/wp-content/themes/stadtkapelle-theme/assets/fonts/Merriweather/Merriweather-BoldItalic.ttf") format("truetype");
  font-weight: 700;
  font-style: italic;
  font-display: swap;
}
@font-face {
  font-family: "Merriweather";
  src: url("/wp-content/themes/stadtkapelle-theme/assets/fonts/Merriweather/Merriweather-Black.ttf") format("truetype");
  font-weight: 900;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: "Merriweather";
  src: url("/wp-content/themes/stadtkapelle-theme/assets/fonts/Merriweather/Merriweather-BlackItalic.ttf") format("truetype");
  font-weight: 900;
  font-style: italic;
  font-display: swap;
}
@font-face {
  font-family: "Inter";
  src: url("/wp-content/themes/stadtkapelle-theme/assets/fonts/Inter/Inter-VariableFont_opsz,wght.ttf") format("truetype-variations");
  font-weight: 100 900;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: "Inter";
  src: url("/wp-content/themes/stadtkapelle-theme/assets/fonts/Inter/Inter-Italic-VariableFont_opsz,wght.ttf") format("truetype-variations");
  font-weight: 100 900;
  font-style: italic;
  font-display: swap;
}
.serif {
  font-family: "Merriweather", Georgia, "Times New Roman", serif;
}

.sans {
  font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
}

html {
  font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
}

/* Reset and base styles */
*,
*::before,
*::after {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  font-size: 16px;
  -webkit-text-size-adjust: 100%;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

img {
  max-width: 100%;
  height: auto;
  display: block;
}

a {
  color: inherit;
  text-decoration: none;
}

button,
input,
select,
textarea {
  font: inherit;
}

ul,
ol {
  list-style: none;
}

/* Remove all animations and transitions for people that prefer not to see them */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}
* {
  box-sizing: border-box;
}

.header__inner {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
@media (min-width: 62rem) {
  .header__inner {
    padding-top: 2rem;
    padding-bottom: 1.5rem;
  }
}
.header .logo__text {
  display: none;
}
@media (min-width: 62rem) {
  .header .logo__mobile {
    display: none;
  }
}
.header .logo__desktop {
  display: none;
  width: 29rem;
  height: auto;
}
@media (min-width: 62rem) {
  .header .logo__desktop {
    display: block;
  }
}

.hamburger {
  background-color: transparent;
  border: none;
  padding: 0;
}
@media (min-width: 62rem) {
  .hamburger {
    transform: translateY(0.5rem);
  }
}
.hamburger img {
  width: 2rem;
  height: 2rem;
}

footer {
  background-color: #CFBB85;
  padding: 3rem 1rem;
  margin-top: 5rem;
  color: #000;
}
footer .container {
  display: block;
  max-width: 62rem;
  margin: 0 auto;
}
@media (min-width: 48rem) {
  footer .container {
    display: grid;
    gap: 6rem;
    grid-template-columns: 1fr 1fr 1fr 1fr;
    justify-content: space-between;
    align-items: start;
  }
}
footer .footer-logo {
  text-align: center;
}
@media (min-width: 48rem) {
  footer .footer-logo {
    flex: 1;
    text-align: left;
  }
}
footer .footer-logo img {
  width: 100%;
  max-width: 10rem;
  height: auto;
  margin-bottom: 1rem;
}
footer .footer-logo .footer-brand {
  font-weight: bold;
  text-transform: uppercase;
  line-height: 1.2;
}
@media (min-width: 48rem) {
  footer nav,
  footer .contact-section {
    flex: 1;
  }
}
footer nav h2,
footer .contact-section h2 {
  font-size: 1.75rem;
  font-weight: bold;
  margin-bottom: 1rem;
}
footer nav ul,
footer .contact-section ul {
  list-style: none;
  padding: 0;
  margin: 0;
}
footer nav ul li,
footer .contact-section ul li {
  margin-bottom: 0.5rem;
}
footer nav ul li a,
footer .contact-section ul li a {
  text-decoration: none;
  color: inherit;
}
footer nav ul li a:hover,
footer .contact-section ul li a:hover {
  text-decoration: underline;
}
footer .contact-section address {
  font-style: normal;
  margin-bottom: 1rem;
}
footer .contact-section .social-links {
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
}
footer .contact-section .social-links a {
  color: #000;
  font-size: 1.25rem;
}
footer .contact-section .social-links a:hover {
  opacity: 0.8;
}
footer .copyright {
  text-align: center;
  margin-top: 3rem;
  padding-top: 1rem;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  font-size: 0.875rem;
}
footer .copyright p {
  margin: 0;
}

.container {
  width: 100%;
  margin-right: auto;
  margin-left: auto;
  padding-right: 1rem;
  padding-left: 1rem;
  max-width: 62rem;
}

.alignfull {
  margin: 2rem calc(50% - 50vw);
  max-width: 100vw;
  width: 100vw;
}

.alignwide {
  margin: 2rem calc(25% - 25vw);
  max-width: 75vw;
  width: 75vw;
}

.aligncenter {
  margin-left: auto;
  margin-right: auto;
  text-align: center;
}

@media (min-width: 36rem) {
  .alignleft {
    float: left;
    margin-right: 1rem;
  }
}

@media (min-width: 36rem) {
  .alignright {
    float: right;
    margin-left: 1rem;
  }
}

.wp-block-columns {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 1rem;
}
.wp-block-columns .wp-block-column {
  flex: 0 0 100%;
  width: 100%;
}
@media (min-width: 48rem) {
  .wp-block-columns {
    flex-wrap: nowrap;
  }
  .wp-block-columns .wp-block-column {
    flex: 1;
    min-width: 0;
  }
  .wp-block-columns .wp-block-column[style*=flex-basis] {
    flex-grow: 0;
  }
}

.wp-block-group {
  width: 100%;
}
.wp-block-group.has-background {
  padding: 1rem;
}

.wp-block-media-text {
  display: grid;
  grid-template-rows: auto;
  align-items: center;
  grid-template-columns: 1fr;
  gap: 1rem;
}
@media (min-width: 48rem) {
  .wp-block-media-text {
    grid-template-columns: 50% 1fr;
  }
}
@media (min-width: 48rem) {
  .wp-block-media-text.has-media-on-the-right {
    grid-template-columns: 1fr 50%;
  }
}

.wp-block-gallery {
  display: grid;
  gap: 1rem;
  margin: 0;
  padding: 0;
  list-style: none;
  grid-template-columns: 1fr;
}
@media (min-width: 36rem) {
  .wp-block-gallery {
    grid-template-columns: repeat(2, 1fr);
  }
}
@media (min-width: 62rem) {
  .wp-block-gallery {
    grid-template-columns: repeat(3, 1fr);
  }
}
.wp-block-gallery .blocks-gallery-item {
  width: 100%;
  margin: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}
.wp-block-gallery .blocks-gallery-item figure {
  width: 100%;
  height: 100%;
  margin: 0;
}
.wp-block-gallery .blocks-gallery-item figure img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.wp-block-cover {
  display: flex;
  position: relative;
  align-items: center;
  justify-content: center;
  min-height: 25rem;
  width: 100%;
  padding: 1rem;
}
.wp-block-cover.alignfull {
  padding-left: 0;
  padding-right: 0;
}
.wp-block-cover .wp-block-cover__inner-container {
  width: 100%;
  max-width: 62rem;
  margin: 0 auto;
  position: relative;
  z-index: 1;
}

.wp-block-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 1rem;
}
.wp-block-buttons.is-content-justification-center {
  justify-content: center;
}
.wp-block-buttons.is-content-justification-right {
  justify-content: flex-end;
}
.wp-block-buttons.is-content-justification-left {
  justify-content: flex-start;
}

.wp-block-list {
  padding-left: 2rem;
  margin: 1rem 0;
}

.wp-block-table {
  width: 100%;
  margin: 1rem 0;
  overflow-x: auto;
  display: block;
}
@media (min-width: 48rem) {
  .wp-block-table {
    display: table;
  }
}
.wp-block-table table {
  width: 100%;
  border-collapse: collapse;
}
.wp-block-table td,
.wp-block-table th {
  padding: 0.5rem;
}

.wp-block-quote {
  margin: 1rem 0;
  padding-left: 1rem;
}
@media (min-width: 48rem) {
  .wp-block-quote {
    padding-left: 2rem;
  }
}

.wp-block-separator {
  margin: 1rem 0;
  width: 100%;
}
.wp-block-separator.is-style-wide {
  width: 100%;
}
.wp-block-separator.is-style-dots {
  width: 100%;
  text-align: center;
}

/* Main Menu styling */
/* Slide Menu Styles */
.slide-menu {
  position: fixed;
  top: 0;
  right: -100%;
  width: 100%;
  height: 100%;
  background-color: #f5f1e9; /* Beige/cream background */
  transition: right 0.3s ease;
  z-index: 1000;
  overflow-y: auto;
  padding: 2rem;
  /* Highlights Menu styling */
  /* Explore Menu styling */
}
.slide-menu.open {
  right: 0;
}
.slide-menu #main-menu {
  background-color: #333;
  padding: 10px;
}
.slide-menu #main-menu ul {
  list-style: none;
  margin: 0;
  padding: 0;
  display: flex;
}
.slide-menu #main-menu li {
  margin-right: 20px;
}
.slide-menu #main-menu a {
  color: #fff;
  text-decoration: none;
}
.slide-menu #highlights {
  background-color: #f0f0f0;
  padding: 10px;
}
.slide-menu #explore {
  background-color: #ddd;
  padding: 10px;
}

.close-menu {
  position: absolute;
  top: 20px;
  right: 30px;
  font-size: 24px;
  background: none;
  border: none;
  cursor: pointer;
  color: #000;
}

.menu-container {
  margin-top: 60px;
  padding: 20px;
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
}

.menu-section {
  margin-bottom: 40px;
  text-align: left;
}
.menu-section ul {
  list-style: none;
  padding: 0;
}
.menu-section li {
  margin-bottom: 1rem;
}
.menu-section a {
  text-decoration: none;
  color: #000;
  font-size: 1.25rem;
  font-weight: 500;
}
.menu-section a:hover {
  opacity: 0.7;
}

/* Social Icons Section */
.social-icons {
  display: flex;
  justify-content: flex-start;
  gap: 1.5rem;
  margin: 2rem 0;
  padding: 1rem 0;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}
.social-icons a {
  color: #000;
  font-size: 1.5rem;
}
.social-icons a:hover {
  opacity: 0.7;
}

/* Overlay to prevent background scrolling when menu is open */
body.menu-open {
  overflow: hidden;
}

/* Responsive Adjustments */
@media (min-width: 768px) {
  /* Ensure hamburger menu is visible on desktop */
  .hamburger {
    display: block;
  }
  .slide-menu {
    max-width: 400px;
  }
}
/* This file is the entrypoint for all the content related styling for the gutenberg editor. */
article.page > h2 {
  display: none;
}
article.page .post-content > *:not(.wp-block-cover) {
  margin-right: auto;
  margin-left: auto;
  width: 100%;
  max-width: 62rem;
  padding-left: 1rem;
  padding-right: 1rem;
}
article.page .post-content .wp-block-heading {
  font-family: "Merriweather", Georgia, "Times New Roman", serif;
  margin-top: 2rem;
  margin-bottom: 1rem;
}
article.page .post-content h1.wp-block-heading {
  font-size: 2rem;
}
article.page .post-content h1.wp-block-heading + .small {
  margin-top: 0;
  margin-bottom: 3rem;
  text-transform: uppercase;
  font-size: 1.25rem;
}
article.page .post-content h2.wp-block-heading {
  font-size: 1.5rem;
}
article.page .post-content h3.wp-block-heading {
  font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
  font-size: 1.25rem;
}
article.page .post-content .wp-block-cover + .wp-block-heading {
  margin-top: 5rem;
}
article.page .post-content figure.wp-block-image {
  margin-top: 2rem;
  margin-bottom: 2rem;
  padding: 0;
  border-left: 0.5rem solid #CFBB85;
}
article.page .post-content p + details {
  margin-top: 2rem;
}
article.page .post-content details {
  margin: 1rem 0;
}
article.page .post-content details summary {
  font-size: 1.5rem;
  font-family: "Merriweather", Georgia, "Times New Roman", serif;
  font-weight: 300;
}

.musiker-block {
  text-align: center;
  max-width: 37.5rem;
  margin: 0 auto;
  padding: 2rem 1rem;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.musiker-block .musiker-image-container {
  width: 15rem;
  height: 15rem;
  margin: 0 auto 1.5rem;
  border-radius: 50%;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}
.musiker-block .musiker-image-container img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.musiker-block .musiker-name {
  font-weight: bold;
  font-size: 1.5rem;
  margin: 0 0 0.5rem;
}
.musiker-block .musiker-instrument {
  color: #666;
  margin-bottom: 1rem;
}
.musiker-block .musiker-description {
  line-height: 1.6;
}
.musiker-block .wp-block-stadtkapelle-theme-musiker .image-button {
  padding: 0;
  border: none;
  border-radius: 50%;
  overflow: hidden;
  width: 15rem;
  height: 15rem;
}
.musiker-block .wp-block-stadtkapelle-theme-musiker .image-button img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.musiker-block .wp-block-stadtkapelle-theme-musiker .button {
  display: block;
  margin: 0 auto;
}

.intro-block {
  position: relative;
  height: 90vh;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  align-items: flex-end;
  margin-bottom: 5rem;
}
.intro-block__desktop-image, .intro-block__mobile-image {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-size: cover;
  background-position: center;
}
.intro-block__desktop-image {
  display: none;
}
@media (min-width: 48rem) {
  .intro-block__desktop-image {
    display: block;
  }
}
.intro-block__mobile-image {
  display: block;
}
@media (min-width: 48rem) {
  .intro-block__mobile-image {
    display: none;
  }
}
.intro-block .intro-logo {
  position: absolute;
  top: 2rem;
  left: 2rem;
  z-index: 1;
}
@media (min-width: 48rem) {
  .intro-block .intro-logo {
    top: 0;
    bottom: 0;
    height: 100%;
    display: flex;
    align-items: center;
  }
}
.intro-block .intro-logo img {
  width: 150px;
  height: auto;
}
@media (min-width: 48rem) {
  .intro-block .intro-logo img {
    width: 15rem;
  }
}
.intro-block .intro-content {
  position: relative;
  z-index: 1;
  color: white;
  text-align: right;
  margin-left: auto;
  padding: 2rem;
}
@media (min-width: 48rem) {
  .intro-block .intro-content {
    max-width: 60%;
  }
}
.intro-block .intro-content .intro-text {
  font-size: 1rem;
  font-family: "Merriweather", Georgia, "Times New Roman", serif;
  margin: 0;
  font-weight: bold;
}
@media (min-width: 48rem) {
  .intro-block .intro-content .intro-text {
    font-size: 2.5rem;
  }
}
.intro-block .image-controls {
  position: absolute;
  top: 1rem;
  right: 1rem;
  z-index: 1;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

/*# sourceMappingURL=output.css.map */
